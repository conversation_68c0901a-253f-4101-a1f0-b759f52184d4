name: Auto update project wiki

on:
  push:
    branches:
      - main
    tags:
      - 'v*' # Initializes on any new tag

jobs:
  update_wiki:
    runs-on: ubuntu-latest
    steps:
      - name: Checkouting main code...
        uses: actions/checkout@v4
      - name: Updating Project Wiki...
        uses: impresscms-dev/phpdocs-wiki-update-action@v2.2
        with:
          class_root_namespace: Imponeer\Contracts\ExtensionInfo\
          include: Imponeer\Contracts\ExtensionInfo\**
