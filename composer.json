{"name": "imponeer/extension-info-contracts", "description": "Interfaces that helps to describe extension", "type": "library", "require": {"php": ">=8.3", "league/flysystem": "^2.0", "ext-fileinfo": "*"}, "license": "MIT", "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "autoload": {"psr-4": {"Imponeer\\Contracts\\ExtensionInfo\\": "src/"}}, "keywords": ["info", "contracts", "extensions", "abstractions", "decoupling", "interfaces"], "require-dev": {"composer/composer": ">2.1.9", "dragonmantank/cron-expression": "^3.1", "squizlabs/php_codesniffer": "^3.13", "phpstan/phpstan": "^2.0"}, "suggest": {"composer/composer": "For using with composer based factories", "dragonmantank/cron-expression": "For using to describe extensions that can have cron jobs"}, "scripts": {"phpcs": "phpcs", "phpcbf": "phpcbf", "phpstan": "phpstan analyse --configuration=phpstan.neon"}}