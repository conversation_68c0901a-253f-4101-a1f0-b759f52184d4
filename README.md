[![License](https://img.shields.io/github/license/imponeer/extension-info-contracts.svg)](LICENSE)
[![GitHub release](https://img.shields.io/github/release/imponeer/extension-info-contracts.svg)](https://github.com/imponeer/extension-info-contracts/releases) [![Maintainability](https://api.codeclimate.com/v1/badges/6bf91993fd03ccfdc7c5/maintainability)](https://codeclimate.com/github/imponeer/extension-info-contracts/maintainability) [![PHP](https://img.shields.io/packagist/php-v/imponeer/extension-info-contracts.svg)](http://php.net) 
[![Packagist](https://img.shields.io/packagist/dm/imponeer/extension-info-contracts.svg)](https://packagist.org/packages/imponeer/extension-info-contracts)

# Extensions Info Contracts

Some extensions (f.e. for CMS) can be installed not only from [Composer](https://getcomposer.org) but by copying all files to specific folder. If there are more than one way to do that, comes problem how easy to get info about all such extensions. For that this is contract library - to make easier to create standardized information about extensions readers. 

## Installation

To install and use this package, we recommend to use [Composer](https://getcomposer.org):

```bash
composer require imponeer/extension-info-contracts
```

Otherwise, you need to include manually files from `src/` directory. 

## Who uses?

Here is a list with libraries that implements this contract:
* [ImpressCMS/extensions-formats](https://github.com/ImpressCMS/extensions-formats) - [ImpressCMS](https://impresscms.org) themes and modules

## How to contribute?

If you want to add some functionality or fix bugs, you can fork, change and create pull request. If you not sure how this works, try [interactive GitHub tutorial](https://skills.github.com).

If you found any bug or have some questions, use [issues tab](https://github.com/imponeer/extension-info-contracts/issues) and write there your questions.
